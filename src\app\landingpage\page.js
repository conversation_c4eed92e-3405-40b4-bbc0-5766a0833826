import Image from "next/image";
import React from "react";
import landinglogo from "../../assets/images/landinglogo.png";
export default function page() {
  return (
    <div
      className="min-h-screen "
      style={{ fontFamily: "General Sans, sans-serif" }}
    >
      {/* Header - Always visible */}
      <div className="bg-white px-4 md:px-16 lg:pt-8 pt-22 md:pb-4">
        <Image src={landinglogo}/>
      </div>

      <div className="flex flex-col md:flex-row lg:min-h-screen">
        {/* Left Side - White Background */}
        <div className="flex-1 bg-white flex flex-col">
          {/* Left Content */}
          <div className="flex-1 flex items-center px-4 md:px-16 py-8 md:py-0">
            <div className="max-w-lg space-y-6 md:space-y-8">
              <div className="space-y-4 md:space-y-6">
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 leading-tight">
                  Links Don't Sell.
                  <br />
                  <span className="text-gray-900">Smart Conversations Do</span>
                </h2>

                <p className="text-gray-600 text-sm md:text-base leading-relaxed">
                  Driply turns your Instagram bio link into an AI assistant that
                  converts followers into customers. Get personalized
                  recommendations that drive real results for your business.
                </p>
              </div>

              <button className="bg-[#DCFB41] text-black font-semibold px-6 py-3 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl">
                Learn More
              </button>
            </div>
          </div>
        </div>

        {/* Right Side - Gray Background */}
        <div className="flex-1 bg-gray-100 relative overflow-hidden min-h-[400px] md:min-h-screen">
          <div className="relative flex items-center justify-center h-full px-4 md:px-8 py-8 md:py-0">
            {/* Instagram Profile Phone (Behind/Left) */}
            <div className="absolute left-4 lg:left-28 top-8 l:top-20 z-10 bg-black rounded-[2rem] md:rounded-[2.5rem] p-1.5 md:p-2 shadow-2xl transform ">
              <div className="bg-black rounded-[1.5rem] md:rounded-[2rem] overflow-hidden w-44 md:w-56 h-[360px] md:h-[290px]">
                {/* Status Bar */}
                <div className="text-white px-3 md:px-4 py-1.5 md:py-2 text-xs md:text-sm flex justify-between items-center">
                  <span>9:41</span>
                  <div className="flex gap-1 items-center">
                    <div className="w-3 md:w-4 h-1.5 md:h-2 bg-white rounded-sm"></div>
                    <div className="w-1 h-1.5 md:h-2 bg-white rounded-sm"></div>
                    <div className="w-4 md:w-6 h-1.5 md:h-2 bg-white rounded-sm"></div>
                  </div>
                </div>

                {/* Instagram Profile */}
                <div className="p-3 md:p-4 space-y-2 md:space-y-3">
                  {/* Header */}
                  <div className="flex items-center gap-2 md:gap-3 text-white">
                    <button className="text-sm md:text-lg">←</button>
                    <h3 className="font-semibold text-xs md:text-sm">
                      The.Curly.Yogini
                    </h3>
                    <div className="w-3 md:w-4 h-3 md:h-4 bg-blue-500 rounded-full flex items-center justify-center ml-1">
                      <span className="text-white text-xs">✓</span>
                    </div>
                  </div>

                  {/* Profile Section */}
                  <div className="flex items-center gap-2 md:gap-3">
                    <div className="relative">
                      <div className="w-12 md:w-16 h-12 md:h-16 rounded-full overflow-hidden border-2 md:border-4 border-gradient-to-r from-pink-500 to-yellow-500 p-0.5 md:p-1">
                        <div className="w-full h-full bg-gradient-to-br from-amber-400 to-orange-600 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs md:text-sm font-bold">
                            CY
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex-1">
                      <div className="flex justify-between text-center text-white">
                        <div>
                          <div className="font-bold text-xs md:text-sm">
                            657
                          </div>
                          <div className="text-xs text-gray-300">Posts</div>
                        </div>
                        <div>
                          <div className="font-bold text-xs md:text-sm">
                            10.2K
                          </div>
                          <div className="text-xs text-gray-300">Followers</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Name */}
                  <div className="text-white">
                    <div className="font-semibold text-xs md:text-sm">
                      The.Curly.Yogini
                    </div>
                  </div>

                  {/* Bio Link */}
                  <div className="bg-gray-800 rounded-full px-2 md:px-3 py-1.5 md:py-2 text-center">
                    <span className="text-blue-400 text-xs">
                      driply.chat/thecurlyyogini
                    </span>
                  </div>

                  {/* Follow Button */}
                  <button className="w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-1.5 md:py-2 rounded-lg transition-colors text-xs md:text-sm">
                    Follow
                  </button>
                </div>
              </div>
            </div>

            {/* Chat Interface Phone (Front/Right) */}
            <div className="absolute right-4 md:right-28 top-4 md:top-0 z-20 bg-black rounded-[2rem] md:rounded-[2.5rem] p-1.5 md:p-2 shadow-2xl transform ">
              <div className="bg-gradient-to-b from-purple-200 to-purple-300 rounded-[1.5rem] md:rounded-[2rem] overflow-hidden w-48 md:w-64 h-[400px] md:h-[520px]">
                {/* Status Bar */}
                <div className="text-black px-3 md:px-4 py-1.5 md:py-2 text-xs md:text-sm flex justify-between items-center">
                  <span>9:41</span>
                  <div className="flex gap-1 items-center">
                    <div className="w-3 md:w-4 h-1.5 md:h-2 bg-black rounded-sm"></div>
                    <div className="w-1 h-1.5 md:h-2 bg-black rounded-sm"></div>
                    <div className="w-4 md:w-6 h-1.5 md:h-2 bg-black rounded-sm"></div>
                  </div>
                </div>

                {/* Chat Content */}
                <div className="p-3 md:p-5 space-y-3 md:space-y-4 h-full flex flex-col">
                  {/* Profile Avatar */}
                  <div className="flex items-center justify-center">
                    <div className="w-10 md:w-14 h-10 md:h-14 rounded-full overflow-hidden">
                      <div className="w-full h-full bg-gradient-to-br from-amber-400 to-orange-600 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs md:text-sm font-bold">
                          CY
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Profile Name */}
                  <div className="text-center">
                    <h3 className="font-semibold text-gray-800 text-xs md:text-sm">
                      The Curly Yogini
                    </h3>
                  </div>

                  {/* Main Message */}
                  <div className="text-center space-y-1">
                    <h2 className="text-sm md:text-lg font-bold text-gray-800">
                      Let's start stretching!
                    </h2>
                    <p className="text-gray-600 text-xs">Lessons? Times?</p>
                    <p className="text-gray-600 text-xs">Or anything else...</p>
                  </div>

                  {/* Quick Action Cards */}
                  <div className="space-y-2 flex-1">
                    <div className="flex gap-1.5 md:gap-2">
                      <div className="flex-1 bg-white/70 rounded-lg md:rounded-xl p-1.5 md:p-2">
                        <div className="font-semibold text-gray-800 text-xs">
                          Lessons
                        </div>
                        <div className="text-xs text-gray-600">
                          All lessons type
                        </div>
                      </div>
                      <div className="flex-1 bg-white/70 rounded-lg md:rounded-xl p-1.5 md:p-2">
                        <div className="font-semibold text-gray-800 text-xs">
                          Morning routine course
                        </div>
                        <div className="text-xs text-gray-600">
                          10% off until today!
                        </div>
                      </div>
                    </div>

                    {/* Input Box */}
                    <div className="bg-white/70 rounded-lg md:rounded-xl p-2.5 md:p-3 mt-auto">
                      <div className="text-gray-600 text-xs mb-2">
                        Ask anything
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex-1"></div>
                        <div className="w-5 md:w-6 h-5 md:h-6 bg-black rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">→</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
